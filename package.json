{"name": "obsidian-task-sync", "version": "1.0.0", "description": "Sync tasks between Obsidian and external systems with project/area organization", "main": "src/main.ts", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "vitest run", "test:ci": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:build": "npm test && npm run build", "test:e2e": "npx vitest run --config vitest.playwright.config.mjs", "test:e2e:headless": "xvfb-run -a --server-args=\"-screen 0 1280x1024x24 -ac -nolisten tcp -dpi 96\" npx vitest run --config vitest.playwright.config.mjs", "test:e2e:windowed": "E2E_HEADLESS=false npx vitest run --config vitest.playwright.config.mjs", "test:e2e:ci": "CI=true npx vitest run --config vitest.playwright.config.mjs --reporter=verbose", "setup:obsidian-playwright": "bash scripts/setup-obsidian-playwright.sh", "setup:headless": "bash scripts/setup-headless-testing.sh", "test:headless-detection": "node scripts/test-headless-detection.js", "test:xvfb": "node scripts/test-xvfb-electron.js", "validate:headless": "node scripts/validate-headless-setup.js", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": ["obsidian", "plugin", "task", "sync", "productivity"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@electron/asar": "^4.0.1", "@playwright/test": "^1.48.0", "@types/node": "^24.3.0", "@types/pluralize": "^0.0.33", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vitest/ui": "^3.2.4", "builtin-modules": "^5.0.0", "chai": "^5.2.1", "electron": "^37.4.0", "esbuild": "^0.25.9", "jsdom": "^26.0.0", "obsidian": "latest", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typescript": "^5.9.2", "vite": "^7.1.3", "vitest": "^3.2.4"}, "dependencies": {"@types/js-yaml": "^4.0.9", "gray-matter": "^4.0.3", "js-yaml": "^4.1.0", "pluralize": "^8.0.0", "remark": "^15.0.1", "remark-frontmatter": "^5.0.0", "remark-parse": "^11.0.0"}}